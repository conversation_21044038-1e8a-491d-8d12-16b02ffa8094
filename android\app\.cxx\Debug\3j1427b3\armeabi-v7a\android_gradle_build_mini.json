{"buildFiles": ["C:\\devlopper\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\new_app\\new_app\\android\\app\\.cxx\\Debug\\3j1427b3\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\new_app\\new_app\\android\\app\\.cxx\\Debug\\3j1427b3\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}