// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDyTfTWbPhPcHREzwIlgzU0bgv9oCQxIyE',
    appId: '1:50699717496:web:d64e6a55d49c32acd6968d',
    messagingSenderId: '50699717496',
    projectId: 'bordj-elmokrani',
    authDomain: 'bordj-elmokrani.firebaseapp.com',
    storageBucket: 'bordj-elmokrani.firebasestorage.app',
    measurementId: 'G-ZX1X5LSE3E',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB-QGsCIu-axkPN0slYn5iRZh0EriTGsaQ',
    appId: '1:50699717496:android:7e2d9318613d98ffd6968d',
    messagingSenderId: '50699717496',
    projectId: 'bordj-elmokrani',
    storageBucket: 'bordj-elmokrani.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCmTr0QmoqtaOHOQdVW3hESFJp8HDLypx8',
    appId: '1:50699717496:ios:6ad7a2dc9b492caad6968d',
    messagingSenderId: '50699717496',
    projectId: 'bordj-elmokrani',
    storageBucket: 'bordj-elmokrani.firebasestorage.app',
    iosBundleId: 'com.example.bordjElmokrani',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCmTr0QmoqtaOHOQdVW3hESFJp8HDLypx8',
    appId: '1:50699717496:ios:6ad7a2dc9b492caad6968d',
    messagingSenderId: '50699717496',
    projectId: 'bordj-elmokrani',
    storageBucket: 'bordj-elmokrani.firebasestorage.app',
    iosBundleId: 'com.example.bordjElmokrani',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDyTfTWbPhPcHREzwIlgzU0bgv9oCQxIyE',
    appId: '1:50699717496:web:2cc41ff67fbf6898d6968d',
    messagingSenderId: '50699717496',
    projectId: 'bordj-elmokrani',
    authDomain: 'bordj-elmokrani.firebaseapp.com',
    storageBucket: 'bordj-elmokrani.firebasestorage.app',
    measurementId: 'G-BWMX123XZN',
  );
}
